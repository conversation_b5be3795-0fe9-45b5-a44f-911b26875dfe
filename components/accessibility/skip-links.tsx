"use client";

import React from "react";

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
  className?: string;
}

const defaultLinks: SkipLink[] = [
  { href: "#main-content", label: "Skip to main content" },
  { href: "#navigation", label: "Skip to navigation" },
  { href: "#footer", label: "Skip to footer" },
];

export const SkipLinks: React.FC<SkipLinksProps> = ({
  links = defaultLinks,
  className = "",
}) => {
  const handleSkipLinkClick = (
    event:
      | React.MouseEvent<HTMLAnchorElement>
      | React.KeyboardEvent<HTMLAnchorElement>
  ) => {
    const href = event.currentTarget.getAttribute("href");
    if (!href) return;

    event.preventDefault();

    // Find the target element
    const targetElement = document.querySelector(href);
    if (targetElement) {
      // Ensure the element is focusable
      const originalTabIndex = targetElement.getAttribute("tabindex");
      const needsTabIndex = originalTabIndex === null;

      if (needsTabIndex) {
        targetElement.setAttribute("tabindex", "-1");
      }

      // Scroll to the element first
      targetElement.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });

      // Focus the element after a short delay to ensure scrolling completes
      setTimeout(() => {
        (targetElement as HTMLElement).focus();
      }, 100);

      // Remove temporary tabindex after focus is set
      if (needsTabIndex) {
        setTimeout(() => {
          targetElement.removeAttribute("tabindex");
        }, 200);
      }
    }
  };

  return (
    <nav aria-label="Skip navigation links" className={className}>
      {links.map((link, index) => (
        <a
          key={index}
          href={link.href}
          className="skip-links"
          onClick={handleSkipLinkClick}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSkipLinkClick(e as any);
            }
          }}
        >
          {link.label}
        </a>
      ))}
    </nav>
  );
};

export default SkipLinks;
